import { useEffect, useMemo, useRef, useState } from "react";
import { ArrowLeft, Plus, Send } from "react-feather";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { useNavigate } from "@tanstack/react-router";
import {
	applyDiscount,
	createOrder,
	uploadPaymentProof,
} from "@/features/payments/services";
import type { DiscountInfo, Order } from "@/features/payments/types";
import { useGetPaymentProofUploadUrl } from "@/lib/queries/payment.query";
import { useToast } from "@/hooks/use-toast";
import { useAuthStore } from "@/features/auth/store";

type CheckoutPageProps = {
	planType?: "daily" | "subscription" | "custom";
	price?: number;
	days?: number;
	onBack: () => void;
};

export default function CheckoutPage({
	planType = "subscription",
	price = 0, // Default to 0
	days,
	onBack,
}: CheckoutPageProps) {
	const [coupon, setCoupon] = useState("");
	const [proofFile, setProofFile] = useState<File | null>(null);
	const [sentToWhatsApp, setSentToWhatsApp] = useState(false);
	const [order, setOrder] = useState<Order | null>(null);
	const [discountInfo, setDiscountInfo] = useState<DiscountInfo | null>(null);
	const [error, setError] = useState<string | null>(null);

	const fileInputRef = useRef<HTMLInputElement>(null);
	const navigate = useNavigate();
	const { mutateAsync: getUploadUrl } = useGetPaymentProofUploadUrl();
	const { toast } = useToast();
	const { user } = useAuthStore();

	const displayDays = useMemo(() => {
		const currentPrice = price ?? 0;
		if (planType === "daily") return 1;
		if (planType === "subscription") {
			if (currentPrice === 1000) return 30; // Monthly
			if (currentPrice === 2000) return 60; // Bi-monthly
			return 30; // Fallback for other subscriptions
		}
		if (planType === "custom" && days) return days;
		return 1; // Default fallback
	}, [planType, price, days]);

	// Create order on component mount
	useEffect(() => {
		const initOrder = async () => {
			try {
				const response = await createOrder({
					subscription_days: displayDays,
					method: "bank_transfer",
				});
				if (response && response.data) {
					const apiResponse = response.data;
					if (apiResponse.success) {
						setOrder(apiResponse.data);
					} else {
						setError(apiResponse.message || "Failed to create order.");
					}
				}
			} catch (err) {
				setError("An unexpected error occurred.");
			}
		};
		initOrder();
	}, [displayDays]);

	const subtotal = order?.original_amount ?? price ?? 0;
	const discount = discountInfo?.discount_amount ?? 0;
	const total = discountInfo?.final_amount ?? order?.amount ?? price ?? 0;

	const isComplete = !!proofFile || sentToWhatsApp;

	// Handlers
	const handleUploadClick = () => fileInputRef.current?.click();

	const handleFilePicked = async (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0] ?? null;
		if (!file) return;

		try {
			// Validate file size and type
			if (file.size > 2 * 1024 * 1024) {
				// 2MB limit
				toast({
					title: "File too large",
					description: "Image must be less than 2MB",
					variant: "destructive",
				});
				return;
			}

			const validTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
			if (!validTypes.includes(file.type)) {
				toast({
					title: "Invalid file type",
					description: "Only JPG, JPEG, PNG and GIF formats are allowed",
					variant: "destructive",
				});
				return;
			}

			if (!order) {
				throw new Error("Order not found");
			}

			// Get upload URL
			const uploadUrlResponse = await getUploadUrl(order.payment_id);
			if (!uploadUrlResponse?.data?.success) {
				throw new Error("Could not get upload URL");
			}

			const uploadUrl = uploadUrlResponse.data.data.upload_url;

			toast({
				title: "Uploading",
				description: "Uploading your proof of payment...",
			});

			await uploadPaymentProof(file, uploadUrl);

			setProofFile(file);

			toast({
				title: "Success",
				description: "Proof of payment uploaded successfully",
			});
		} catch (error) {
			console.error("Error uploading proof of payment:", error);
			toast({
				title: "Upload Failed",
				description:
					error instanceof Error
						? error.message
						: "Failed to upload proof of payment",
				variant: "destructive",
			});
		} finally {
			if (fileInputRef.current) {
				fileInputRef.current.value = "";
			}
		}
	};

	const handleRemoveFile = () => {
		setProofFile(null);
		if (fileInputRef.current) {
			fileInputRef.current.value = "";
		}
	};

	const handleApplyDiscount = async () => {
		if (!order || !coupon) return;
		try {
			const response = await applyDiscount({
				payment_id: order.payment_id,
				discount_code: coupon,
			});
			if (response && response.data) {
				const apiResponse = response.data;
				if (apiResponse.success) {
					setDiscountInfo(apiResponse.data);
					setError(null);
				} else {
					setDiscountInfo(null);
					setError(apiResponse.message || "Invalid coupon.");
				}
			}
		} catch (err) {
			setError("Failed to apply coupon.");
		}
	};

	const handleWhatsApp = () => {
		const userEmail = user?.email || "Not provided";
		const userId = user?.uid || "Not provided";
		const text = encodeURIComponent(
			`Hi! I have made the payment.\n\nUser ID: ${userId}\nEmail: ${userEmail}\nOrder ID: ${order?.order_id}\nPlan: ${displayDays} days\nSubtotal: Rs. ${subtotal.toFixed(
				2
			)}\nDiscount: Rs. ${discount.toFixed(2)}\nTotal: Rs. ${total.toFixed(
				2
			)}\n\nAttaching proof of payment.`
		);
		window.open(`https://wa.me/************?text=${text}`, "_blank");
		setSentToWhatsApp(true); // Mark as sent to WhatsApp
	};

	const proceed = () => {
		if (!isComplete || !order) return;

		// Navigate to the payment success route with the paymentId and totals
		navigate({
			to: "/payment-success",
			// cast to any to avoid strict route search typing here
			search: {
				paymentId: order.payment_id,
				amount: subtotal,
				discount: discount,
				total: total,
				ts: new Date().toISOString(),
				orderId: order.order_id,
			} as any,
		});
	};

	// Phone number for bank transfer and copy handler
	const bankPhoneNumber = "***********";

	const copyPhone = async () => {
		try {
			if (typeof navigator !== "undefined" && navigator.clipboard) {
				await navigator.clipboard.writeText(bankPhoneNumber);
				toast({
					title: "Copied",
					description: "Phone number copied to clipboard.",
				});
			} else {
				// Fallback: select text (rare in modern browsers)
				toast({
					title: "Copy not supported",
					description: "Your browser doesn't support programmatic copy.",
					variant: "destructive",
				});
			}
		} catch (err) {
			toast({
				title: "Copy failed",
				description: "Could not copy phone number.",
				variant: "destructive",
			});
		}
	};

	return (
		<div className='min-h-screen font-["Inter",sans-serif] p-6'>
			{/* Top bar */}
			<div className="max-w-5xl mx-auto relative pt-10 sm:pt-12">
				{/* Back (absolute, top-left) */}
				<div className="absolute left-0 top-2 sm:top-3">
					<button
						onClick={onBack}
						className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-800"
					>
						<ArrowLeft size={18} />
						<span className="text-sm">Back</span>
					</button>
				</div>
			</div>

			{/* Cart card */}
			<div className="mt-6 max-w-5xl mx-auto">
				<Card className="rounded-2xl shadow-xl border-0">
					<CardContent className="p-8">
						{/* Title row */}
						<div className="flex items-start justify-between">
							<div>
								<h1 className="text-[32px] sm:text-[40px] font-extrabold text-[#111827] leading-tight">
									Your Cart
								</h1>
								<p className="text-sm text-gray-600 mt-1">
									Please proceed with your payment now.
								</p>
								{order && (
									<div className="mt-2 text-sm text-gray-500 space-y-1">
										<p>Order ID: {order.order_id}</p>
										<p>Status: {order.status}</p>
										<p>Payment ID: {order.payment_id}</p>
									</div>
								)}
							</div>
						</div>

						{/* Body grid */}
						<div className="mt-8 grid grid-cols-1 lg:grid-cols-12 gap-8">
							{/* Left column */}
							<div className="lg:col-span-7 space-y-6">
								{/* Plan Duration */}
								<div>
									<Label className="block text-sm font-medium text-gray-700 mb-2">
										Plan Duration
									</Label>
									<div className="w-full h-12 text-base text-gray-700 bg-gray-100 rounded-xl border border-gray-200 flex items-center px-4">
										{displayDays} {displayDays === 1 ? "Day" : "Days"}
									</div>
								</div>

								{/* Bank transfer instructions */}
								<div>
									<Label className="block text-sm font-medium text-gray-700 mb-2">
										Please transfer the payment on mentioned details:
									</Label>
									<div className="min-h-[84px] rounded-xl border border-gray-200 bg-[#F9FAFB] px-4 py-3 text-gray-600 text-sm">
										<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
											<div className="flex-1">
												<div className="flex items-center gap-2 flex-wrap">
													<span className="font-medium text-gray-700">
														Easypesa / Jazzcash:
													</span>
													<span className="font-mono">{bankPhoneNumber}</span>
												</div>
												<div className="mt-2">Title: Zalaid Saleem</div>
											</div>
											<div className="sm:ml-4">
												<button
													type="button"
													onClick={copyPhone}
													className="inline-flex items-center gap-2 px-3 py-2 rounded-md bg-white border border-gray-200 text-sm text-gray-700 hover:shadow-sm transition-shadow"
												>
													Copy
												</button>
											</div>
										</div>
									</div>
								</div>

								{/* Upload & WhatsApp buttons */}
								<div>
									<Label className="block text-sm font-medium text-gray-700 mb-3">
										Please upload proof of payment (receipt):
									</Label>

									<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
										<button
											type="button"
											onClick={handleUploadClick}
											className={`h-24 rounded-2xl border border-gray-200 bg-white hover:border-[color:#E5E7EB] hover:shadow-sm transition flex items-center justify-center gap-3 text-[#111827] font-medium ${
												proofFile ? "border-green-500 bg-green-50" : ""
											}`}
										>
											<Plus size={20} />
											{proofFile ? "File Uploaded ✓" : "Proof of Payment"}
										</button>

										<button
											type="button"
											onClick={handleWhatsApp}
											className={`h-24 rounded-2xl border border-gray-200 bg-white hover:border-[color:#E5E7EB] hover:shadow-sm transition flex items-center justify-center gap-3 text-[#111827] font-medium ${
												sentToWhatsApp ? "border-green-500 bg-green-50" : ""
											}`}
										>
											<Send size={18} />
											{sentToWhatsApp
												? "Sent to WhatsApp ✓"
												: "Send on Whatsapp"}
										</button>

										<input
											ref={fileInputRef}
											type="file"
											accept="image/*,.pdf"
											className="hidden"
											onChange={handleFilePicked}
										/>
									</div>

									{/* File Preview */}
									{proofFile && (
										<div className="mt-4 p-4 border border-gray-200 rounded-xl bg-gray-50">
											<div className="flex items-center justify-between">
												<div className="flex items-center gap-3">
													<div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
														{proofFile.type.startsWith("image/") ? (
															<span className="text-blue-600 font-semibold text-xs">
																IMG
															</span>
														) : (
															<span className="text-red-600 font-semibold text-xs">
																PDF
															</span>
														)}
													</div>
													<div>
														<p className="text-sm font-medium text-gray-900 truncate max-w-[200px]">
															{proofFile.name}
														</p>
														<p className="text-xs text-gray-500">
															{(proofFile.size / 1024 / 1024).toFixed(2)} MB
														</p>
													</div>
												</div>
												<button
													type="button"
													onClick={handleRemoveFile}
													className="text-red-500 hover:text-red-700 text-sm font-medium px-3 py-1 rounded-md hover:bg-red-50 transition"
												>
													Remove
												</button>
											</div>

											{/* Image Preview */}
											{proofFile.type.startsWith("image/") && (
												<div className="mt-3">
													<img
														src={URL.createObjectURL(proofFile)}
														alt="Payment proof preview"
														className="max-w-full h-32 object-cover rounded-lg border"
													/>
												</div>
											)}
										</div>
									)}

									{!proofFile && !sentToWhatsApp && (
										<p className="mt-2 text-xs text-amber-600">
											Please upload proof of payment OR send details via
											WhatsApp to proceed, along with the order ID.
										</p>
									)}
								</div>
							</div>

							{/* Right column: Summary + Coupon */}
							<div className="lg:col-span-5">
								{/* Coupon */}
								<div className="mb-5">
									<Label className="block text-sm font-medium text-gray-700 mb-2">
										Discount Coupon
									</Label>
									<div className="flex items-center gap-2">
										<input
											value={coupon}
											onChange={(e) => setCoupon(e.target.value)}
											placeholder="Enter your coupon"
											className="w-full h-12 rounded-xl border border-gray-200 bg-white px-4 text-[15px] outline-none focus:ring-2 focus:ring-[color:#7B53FF]/30 focus:border-[color:#7B53FF]"
										/>
										<Button
											onClick={handleApplyDiscount}
											disabled={!order || !coupon}
										>
											Apply
										</Button>
									</div>
									{error && (
										<p className="mt-1 text-xs text-red-500">{error}</p>
									)}
									{discountInfo && (
										<p className="mt-1 text-xs text-green-600">
											Coupon applied.
										</p>
									)}
								</div>

								{/* Totals box */}
								<div className="rounded-2xl border border-gray-200 bg-white p-6">
									<div className="flex items-center justify-between text-sm text-gray-700">
										<span>Subtotal</span>
										<span className="tabular-nums">
											Rs. {subtotal.toFixed(2)}
										</span>
									</div>
									<div className="mt-2 flex items-center justify-between text-sm text-gray-700">
										<span>Discount</span>
										<span className="tabular-nums">
											Rs. {discount.toFixed(2)}
										</span>
									</div>

									<div className="my-4 h-px bg-gray-200" />

									<div className="flex items-end justify-between">
										<div className="text-sm text-[#111827]">
											<span className="font-semibold">Total</span>
											<span className="text-gray-500"> (incl. VAT)</span>
										</div>
										<div className="flex items-baseline gap-2">
											<span className="text-sm text-gray-600">Rs.</span>
											<span className="text-[32px] font-extrabold text-[#111827] leading-none tabular-nums">
												{total.toFixed(2)}
											</span>
										</div>
									</div>

									<Button
										disabled={!isComplete}
										className={`mt-5 w-full h-11 rounded-xl font-medium text-white shadow-sm ${
											!isComplete ? "opacity-60 cursor-not-allowed" : ""
										}`}
										style={{
											background:
												"linear-gradient(180deg,#4338CA 0%,#4338CA 100%)",
										}}
										onClick={proceed}
									>
										Proceed to Checkout
									</Button>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
